rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // DEVELOPMENT RULES - Very permissive for debugging
    // These rules allow all operations for development purposes

    // Allow all operations on all documents (for development only)
    match /{document=**} {
      allow read, write: if true;
    }

    // Specific rules for projects collection (redundant but explicit)
    match /projects/{projectId} {
      allow read, write: if true;
    }

    // Specific rules for admin_users collection
    match /admin_users/{userId} {
      allow read, write: if true;
    }

    // Specific rules for services collection
    match /services/{serviceId} {
      allow read, write: if true;
    }

    // Specific rules for content collection
    match /content/{contentId} {
      allow read, write: if true;
    }
  }
}
