rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Helper function to check if user is admin
    function isAdmin() {
      return request.auth != null &&
             (request.auth.token.email == '<EMAIL>' ||
              exists(/databases/$(database)/documents/admin_users/$(request.auth.uid)));
    }

    // Helper function to check if user is the demo admin
    function isDemoAdmin() {
      return request.auth != null &&
             request.auth.token.email == '<EMAIL>';
    }
    
    // Default rule - public read, admin write
    match /{document=**} {
      allow read: if true; // Allow public read access for portfolio data
      allow write: if isAdmin(); // Only admin users can write
    }
    
    // Specific rules for projects collection
    match /projects/{projectId} {
      allow read: if true; // Public read access for portfolio display
      allow write: if isAdmin(); // Only admin users can write
    }
    
    // Specific rules for services collection
    match /services/{serviceId} {
      allow read: if true; // Public read access for portfolio display
      allow write: if isAdmin(); // Only admin users can write
    }
    
    // Specific rules for content collection
    match /content/{contentId} {
      allow read: if true; // Public read access for portfolio display
      allow write: if isAdmin(); // Only admin users can write
    }
    
    // Admin users collection - secure rules for production
    match /admin_users/{userId} {
      // Allow read if user is authenticated and accessing their own document
      allow read: if request.auth != null && request.auth.uid == userId;
      
      // Allow write/update if user is accessing their own document and is admin
      allow write: if request.auth != null && 
                      request.auth.uid == userId &&
                      (isDemoAdmin() || isAdmin());
      
      // Allow creation of admin document for demo admin email
      allow create: if request.auth != null && 
                       request.auth.uid == userId &&
                       isDemoAdmin();
    }
  }
}
