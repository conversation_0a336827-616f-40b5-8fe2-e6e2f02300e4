import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:mysite/core/models/project_model.dart';
import 'package:mysite/core/color/colors.dart';
import 'package:sizer/sizer.dart';

class ProjectDetailsDialog extends StatefulWidget {
  final ProjectModel project;

  const ProjectDetailsDialog({
    Key? key,
    required this.project,
  }) : super(key: key);

  @override
  State<ProjectDetailsDialog> createState() => _ProjectDetailsDialogState();
}

class _ProjectDetailsDialogState extends State<ProjectDetailsDialog>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutBack,
    ));
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOut,
    ));
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _openURL(String? url) async {
    if (url != null && url.isNotEmpty) {
      try {
        final uri = Uri.parse(url);
        if (await canLaunchUrl(uri)) {
          await launchUrl(uri, mode: LaunchMode.externalApplication);
        } else {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('Could not launch $url'),
                backgroundColor: Colors.red,
              ),
            );
          }
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Error opening URL: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  void _openImageInBrowser(String imageUrl) async {
    if (imageUrl.isNotEmpty) {
      try {
        final uri = Uri.parse(imageUrl);
        if (await canLaunchUrl(uri)) {
          await launchUrl(uri, mode: LaunchMode.externalApplication);
        } else {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('Could not open image: $imageUrl'),
                backgroundColor: Colors.red,
              ),
            );
          }
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Error opening image: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final size = MediaQuery.of(context).size;
    final isDesktop = size.width > 800;
    final isMobile = size.width < 600;

    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return FadeTransition(
          opacity: _fadeAnimation,
          child: Dialog(
            backgroundColor: Colors.transparent,
            insetPadding: EdgeInsets.symmetric(
              horizontal: isDesktop ? size.width * 0.1 : (isMobile ? 16 : 32),
              vertical: isDesktop ? size.height * 0.05 : (isMobile ? 20 : 40),
            ),
            child: ScaleTransition(
              scale: _scaleAnimation,
              child: Container(
                width: isDesktop
                    ? 900
                    : (isMobile ? size.width - 32 : size.width - 64),
                constraints: BoxConstraints(
                  maxHeight: isDesktop
                      ? size.height * 0.7
                      : (isMobile ? size.height * 0.6 : size.height * 0.65),
                ),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(isMobile ? 16 : 24),
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      theme.scaffoldBackgroundColor,
                      theme.scaffoldBackgroundColor.withOpacity(0.95),
                    ],
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: primaryColor.withOpacity(0.2),
                      blurRadius: 30,
                      offset: const Offset(0, 15),
                    ),
                    BoxShadow(
                      color: Colors.black.withOpacity(0.1),
                      blurRadius: 10,
                      offset: const Offset(0, 5),
                    ),
                  ],
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(isMobile ? 16 : 24),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // Header with banner image
                      _buildHeader(theme, isDesktop, isMobile),

                      // Content
                      Flexible(
                        child: SingleChildScrollView(
                          scrollDirection: Axis.vertical,
                          physics: const BouncingScrollPhysics(),
                          padding: EdgeInsets.all(
                              isDesktop ? 32 : (isMobile ? 16 : 24)),
                          child: _buildContent(theme, isDesktop, isMobile),
                        ),
                      ),

                      // Actions
                      _buildActions(theme, isDesktop, isMobile),
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildHeader(ThemeData theme, bool isDesktop, bool isMobile) {
    return Container(
      height: isDesktop ? 220 : (isMobile ? 120 : 160),
      width: double.infinity,
      child: Stack(
        fit: StackFit.expand,
        children: [
          // Banner Image
          widget.project.bannerUrl.isNotEmpty
              ? (widget.project.bannerUrl.startsWith('http')
                  ? CachedNetworkImage(
                      imageUrl: widget.project.bannerUrl,
                      fit: BoxFit.cover,
                      placeholder: (context, url) => Container(
                        color: Colors.grey[300],
                        child: const Center(
                          child: CircularProgressIndicator(),
                        ),
                      ),
                      errorWidget: (context, url, error) => Container(
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                            colors: [
                              primaryColor.withOpacity(0.8),
                              primaryColor.withOpacity(0.6),
                            ],
                          ),
                        ),
                        child: const Center(
                          child: Icon(Icons.web_asset,
                              size: 50, color: Colors.white),
                        ),
                      ),
                    )
                  : Image.asset(
                      widget.project.bannerUrl,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) => Container(
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                            colors: [
                              primaryColor.withOpacity(0.8),
                              primaryColor.withOpacity(0.6),
                            ],
                          ),
                        ),
                        child: const Center(
                          child: Icon(Icons.web_asset,
                              size: 50, color: Colors.white),
                        ),
                      ),
                    ))
              : Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        primaryColor.withOpacity(0.8),
                        primaryColor.withOpacity(0.6),
                      ],
                    ),
                  ),
                  child: const Center(
                    child: Icon(Icons.web_asset, size: 50, color: Colors.white),
                  ),
                ),

          // Gradient Overlay
          Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  Colors.transparent,
                  Colors.black.withOpacity(0.3),
                  Colors.black.withOpacity(0.7),
                ],
                stops: const [0.0, 0.6, 1.0],
              ),
            ),
          ),

          // Close Button
          Positioned(
            top: isMobile ? 8 : 16,
            right: isMobile ? 8 : 16,
            child: Container(
              decoration: BoxDecoration(
                color: Colors.black.withOpacity(0.5),
                borderRadius: BorderRadius.circular(20),
              ),
              child: IconButton(
                onPressed: () => Navigator.of(context).pop(),
                icon: Icon(Icons.close,
                    color: Colors.white, size: isMobile ? 20 : 24),
                tooltip: 'Close',
                padding: EdgeInsets.all(isMobile ? 8 : 12),
                constraints: BoxConstraints(
                  minWidth: isMobile ? 32 : 40,
                  minHeight: isMobile ? 32 : 40,
                ),
              ),
            ),
          ),

          // Project Icon and Title
          Positioned(
            bottom: isMobile ? 8 : 16,
            left: isMobile ? 12 : 24,
            right: isMobile ? 12 : 24,
            child: Row(
              children: [
                // Project Icon
                if (widget.project.iconUrl.isNotEmpty)
                  Container(
                    width: isMobile ? 40 : 60,
                    height: isMobile ? 40 : 60,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(isMobile ? 8 : 12),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.2),
                          blurRadius: 8,
                          offset: const Offset(0, 4),
                        ),
                      ],
                    ),
                    padding: EdgeInsets.all(isMobile ? 6 : 8),
                    child: widget.project.iconUrl.startsWith('http')
                        ? CachedNetworkImage(
                            imageUrl: widget.project.iconUrl,
                            fit: BoxFit.contain,
                            errorWidget: (context, url, error) =>
                                Icon(Icons.web_asset, size: isMobile ? 20 : 32),
                          )
                        : Image.asset(
                            widget.project.iconUrl,
                            fit: BoxFit.contain,
                            errorBuilder: (context, error, stackTrace) =>
                                Icon(Icons.web_asset, size: isMobile ? 20 : 32),
                          ),
                  ),

                SizedBox(width: isMobile ? 8 : 16),

                // Title
                Expanded(
                  child: Text(
                    widget.project.title,
                    style: TextStyle(
                      fontSize: isDesktop ? 28 : (isMobile ? 18 : 22),
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                      shadows: [
                        Shadow(
                          color: Colors.black.withOpacity(0.5),
                          blurRadius: 4,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    maxLines: isMobile ? 1 : 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildContent(ThemeData theme, bool isDesktop, bool isMobile) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Description Section
        _buildSection(
          title: 'Description',
          icon: Icons.description,
          child: Text(
            widget.project.description,
            style: TextStyle(
              fontSize: isDesktop ? 16 : (isMobile ? 13 : 14),
              color: theme.textTheme.bodyLarge?.color,
              height: 1.6,
            ),
          ),
          isMobile: isMobile,
        ),

        // Divider after Description
        _buildDivider(isMobile),

        // Technologies Section
        if (widget.project.technologies.isNotEmpty) ...[
          _buildSection(
            title: 'Technologies Used',
            icon: Icons.code,
            child: Wrap(
              spacing: isMobile ? 6 : 8,
              runSpacing: isMobile ? 6 : 8,
              children: widget.project.technologies.map((tech) {
                return Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: isMobile ? 8 : 12,
                    vertical: isMobile ? 4 : 6,
                  ),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        primaryColor.withOpacity(0.1),
                        primaryColor.withOpacity(0.05),
                      ],
                    ),
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(
                      color: primaryColor.withOpacity(0.3),
                      width: 1,
                    ),
                  ),
                  child: Text(
                    tech,
                    style: TextStyle(
                      fontSize: isDesktop ? 14 : (isMobile ? 11 : 12),
                      color: primaryColor,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                );
              }).toList(),
            ),
            isMobile: isMobile,
          ),

          // Divider after Technologies
          _buildDivider(isMobile),
        ],

        // Project Images Section
        if (widget.project.sliderImages.isNotEmpty)
          _buildSection(
            title: 'Project Images (${widget.project.sliderImages.length})',
            icon: Icons.photo_library,
            child: _buildImageGrid(isDesktop, isMobile),
            isMobile: isMobile,
          ),
      ],
    );
  }

  Widget _buildImageGrid(bool isDesktop, bool isMobile) {
    final screenWidth = MediaQuery.of(context).size.width;

    // Calculate responsive grid layout
    int crossAxisCount;
    double childAspectRatio;
    double spacing = isDesktop ? 16.0 : (isMobile ? 12.0 : 14.0);
    double borderRadius = isDesktop ? 16.0 : (isMobile ? 12.0 : 14.0);

    if (isDesktop) {
      crossAxisCount = 3;
      childAspectRatio = 0.7; // Slightly portrait for mobile screenshots
    } else if (isMobile) {
      crossAxisCount = 2;
      childAspectRatio = 0.75; // Good for mobile screenshots
    } else {
      crossAxisCount = 2;
      childAspectRatio = 0.7;
    }

    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      padding: EdgeInsets.all(isMobile ? 8 : 12),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: crossAxisCount,
        crossAxisSpacing: spacing,
        mainAxisSpacing: spacing,
        childAspectRatio: childAspectRatio,
      ),
      itemCount: widget.project.sliderImages.length,
      itemBuilder: (context, index) {
        final imageUrl = widget.project.sliderImages[index];
        return GestureDetector(
          onTap: () => _openImageInBrowser(imageUrl),
          child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(borderRadius),
              boxShadow: [
                BoxShadow(
                  color: primaryColor.withOpacity(0.15),
                  blurRadius: isDesktop ? 12 : (isMobile ? 6 : 8),
                  offset: const Offset(0, 4),
                  spreadRadius: 1,
                ),
                BoxShadow(
                  color: Colors.black.withOpacity(0.08),
                  blurRadius: isDesktop ? 6 : (isMobile ? 3 : 4),
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(borderRadius),
              child: Container(
                decoration: BoxDecoration(
                  color: Colors.white,
                  border: Border.all(
                    color: primaryColor.withOpacity(0.2),
                    width: 1.5,
                  ),
                ),
                child: imageUrl.startsWith('http')
                    ? CachedNetworkImage(
                        imageUrl: imageUrl,
                        fit: BoxFit
                            .cover, // Cover for better display of different aspect ratios
                        width: double.infinity,
                        height: double.infinity,
                        placeholder: (context, url) => Container(
                          color: Colors.grey[50],
                          child: Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                CircularProgressIndicator(
                                  strokeWidth: 2,
                                  valueColor: AlwaysStoppedAnimation<Color>(
                                      primaryColor),
                                ),
                                SizedBox(height: 8),
                                Text(
                                  'Loading...',
                                  style: TextStyle(
                                    fontSize: isMobile ? 10 : 12,
                                    color: Colors.grey[600],
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                        errorWidget: (context, url, error) => Container(
                          color: Colors.grey[100],
                          child: Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  Icons.image_not_supported_outlined,
                                  size: isMobile ? 32.0 : 48.0,
                                  color: Colors.grey[400],
                                ),
                                SizedBox(height: 8),
                                Text(
                                  'Image not available',
                                  style: TextStyle(
                                    fontSize: isMobile ? 10 : 12,
                                    color: Colors.grey[600],
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                              ],
                            ),
                          ),
                        ),
                      )
                    : Image.asset(
                        imageUrl,
                        fit: BoxFit
                            .cover, // Cover for better display of different aspect ratios
                        width: double.infinity,
                        height: double.infinity,
                        errorBuilder: (context, error, stackTrace) => Container(
                          color: Colors.grey[100],
                          child: Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  Icons.image_not_supported_outlined,
                                  size: isMobile ? 32.0 : 48.0,
                                  color: Colors.grey[400],
                                ),
                                SizedBox(height: 8),
                                Text(
                                  'Image not found',
                                  style: TextStyle(
                                    fontSize: isMobile ? 10 : 12,
                                    color: Colors.grey[600],
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildSection({
    required String title,
    required IconData icon,
    required Widget child,
    required bool isMobile,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Container(
              padding: EdgeInsets.all(isMobile ? 6 : 8),
              decoration: BoxDecoration(
                color: primaryColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(isMobile ? 6 : 8),
              ),
              child: Icon(
                icon,
                size: isMobile ? 16 : 20,
                color: primaryColor,
              ),
            ),
            SizedBox(width: isMobile ? 8 : 12),
            Text(
              title,
              style: TextStyle(
                fontSize: isMobile ? 16 : 18,
                fontWeight: FontWeight.bold,
                color: primaryColor,
              ),
            ),
          ],
        ),
        SizedBox(height: isMobile ? 12 : 16),
        child,
      ],
    );
  }

  Widget _buildDivider(bool isMobile) {
    return Container(
      margin: EdgeInsets.symmetric(vertical: isMobile ? 20 : 28),
      child: Row(
        children: [
          Expanded(
            child: Container(
              height: 1,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    Colors.transparent,
                    primaryColor.withOpacity(0.3),
                    Colors.transparent,
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActions(ThemeData theme, bool isDesktop, bool isMobile) {
    return Container(
      padding: EdgeInsets.all(isMobile ? 16 : (isDesktop ? 32 : 24)),
      decoration: BoxDecoration(
        color: theme.scaffoldBackgroundColor.withOpacity(0.8),
        border: Border(
          top: BorderSide(
            color: primaryColor.withOpacity(0.1),
            width: 1,
          ),
        ),
      ),
      child: isMobile
          ? Column(
              children: [
                // Open Project Button (full width on mobile)
                if (widget.project.link != null &&
                    widget.project.link!.isNotEmpty)
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton.icon(
                      onPressed: () => _openURL(widget.project.link),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: primaryColor,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(
                            horizontal: 24, vertical: 14),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        elevation: 4,
                      ),
                      icon: const Icon(Icons.launch, size: 18),
                      label: const Text(
                        'Open Project',
                        style: TextStyle(
                          fontWeight: FontWeight.w600,
                          fontSize: 16,
                        ),
                      ),
                    ),
                  ),

                if (widget.project.link != null &&
                    widget.project.link!.isNotEmpty)
                  const SizedBox(height: 12),

                // Close Button (full width on mobile)
                SizedBox(
                  width: double.infinity,
                  child: TextButton(
                    onPressed: () => Navigator.of(context).pop(),
                    style: TextButton.styleFrom(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 24, vertical: 14),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                        side: BorderSide(color: primaryColor.withOpacity(0.3)),
                      ),
                    ),
                    child: const Text(
                      'Close',
                      style: TextStyle(
                        fontWeight: FontWeight.w600,
                        fontSize: 16,
                      ),
                    ),
                  ),
                ),
              ],
            )
          : Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                // Close Button
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  style: TextButton.styleFrom(
                    padding: const EdgeInsets.symmetric(
                        horizontal: 24, vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  child: const Text(
                    'Close',
                    style: TextStyle(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),

                const SizedBox(width: 16),

                // Open Project Button
                if (widget.project.link != null &&
                    widget.project.link!.isNotEmpty)
                  ElevatedButton.icon(
                    onPressed: () => _openURL(widget.project.link),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: primaryColor,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(
                          horizontal: 24, vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      elevation: 4,
                    ),
                    icon: const Icon(Icons.launch, size: 20),
                    label: const Text(
                      'Open Project',
                      style: TextStyle(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
              ],
            ),
    );
  }
}
